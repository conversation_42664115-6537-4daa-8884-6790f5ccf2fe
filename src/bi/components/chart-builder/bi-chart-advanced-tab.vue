<script setup>
const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
  formInstance: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['referenceLineConfigChange']);

const colors = ['#101828', '#004EEB', '#D92D20', '#DC6803', '#039855', '#7839EE', '#4CA30D', '#0E9384', '#BA24D5', '#E31B54', '#344054', '#2E90FA', '#F97066', '#FDB022', '#32D583', '#A48AFB', '#66C61C', '#2ED3B7', '#E478FA', '#FD6F8E', '#667085', '#84CAFF', '#FDA29B', '#FEC84B', '#6CE9A6', '#DDD6FE', '#85E13A', '#5FE9D0', '#EEAAFD', '#FEA3B4'];

function onReferenceLineItemChange(event, index) {
  emit('referenceLineConfigChange', event, index);
}

async function onAddReferenceLine(index) {
  await nextTick();
  emit('referenceLineConfigChange', {
    color: colors[index],
    label: `Reference line ${index + 1}`,
    value: null,
    line_style: 'solid',
  }, index);
}
</script>

<template>
  <template v-if="['bar_chart', 'line_chart', 'area_chart'].includes(props.chartType)">
    <RadiogroupElement
      view="tabs"
      name="orientation"
      label="Orientation"
      :items="{
        vertical: 'Vertical',
        horizontal: 'Horizontal',
      }"
      default="vertical"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
  </template>
  <template v-if="['bar_chart', 'line_chart', 'area_chart', 'scatter_chart'].includes(props.chartType)">
    <SelectElement
      name="data_zoom"
      label="Data zoom"
      :items="{
        disabled: 'Disabled',
        slider: 'Slider',
        inside: 'Inside',
        both: 'Both',
      }"
      default="disabled"
      :native="false"
      :can-clear="false"
      :can-deselect="false"
    />
    <ToggleElement
      name="accessibility_patterns"
      label="Accessibility patterns"
      :columns="{
        default: { container: 12, label: 11, wrapper: 12 },
        sm: { container: 12, label: 11, wrapper: 12 },
      }"
    />
    <ListElement
      name="reference_lines"
      label="Reference lines"
      add-text="Add reference line"
      :controls="{ add: true, remove: false, sort: true }"
      :add-classes="{ ListElement: { handle: 'left-8 top-[1px] visible opacity-100 !z-0' } }"
      :columns="{
        default: { container: 12, label: 12, wrapper: 12 },
        sm: { container: 12, label: 12, wrapper: 12 },
        md: { container: 12, label: 12, wrapper: 12 },
      }"
      :initial="0"
      @add="onAddReferenceLine"
    >
      <template #default="{ index }">
        <ObjectElement :name="index">
          <TextElement
            name="label"
            @change="onReferenceLineItemChange({ label: $event }, index)"
          >
            <template #addon-before>
              <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: props.chartConfig?.reference_lines?.[index]?.color }" />
            </template>
            <template #addon-after>
              <div class="flex items-center gap-1">
                <BiReferenceLineContextMenu
                  :reference-line-config="props.chartConfig?.reference_lines?.[index]"
                  @field-selected="onReferenceLineItemChange($event, index)"
                />
                <IconHawkXClose
                  class="w-4 h-4 cursor-pointer"
                  @click="props.formInstance.elements$.reference_lines.remove(index)"
                />
              </div>
            </template>
          </TextElement>
          <HiddenElement name="color" />
          <HiddenElement name="value" />
          <HiddenElement name="line_style" />
        </ObjectElement>
      </template>
    </ListElement>
  </template>
  <template v-else>
    Advanced - {{ props.chartType }}
  </template>
</template>
